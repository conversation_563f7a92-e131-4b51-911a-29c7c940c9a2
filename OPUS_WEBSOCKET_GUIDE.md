# WebSocket音频流 - 真正的Opus编码实现指南

## 🎯 概述

本指南介绍如何使用真正的Opus编码来实现ESP32和Python服务器之间的WebSocket音频流传输。

## 📋 系统要求

### Python端
- Python 3.7+
- 必需库：
  ```bash
  pip install websockets numpy opuslib
  ```

### ESP32端
- ESP-IDF框架
- 已配置的Opus解码器
- WebSocket客户端支持

## 🔧 安装步骤

### 1. 安装Python依赖

```bash
# 安装基础依赖
pip install websockets numpy

# 安装Opus编码库
pip install opuslib
```

**注意**：如果`opuslib`安装失败，可能需要先安装系统级的Opus库：

**Ubuntu/Debian:**
```bash
sudo apt-get install libopus-dev
pip install opuslib
```

**macOS:**
```bash
brew install opus
pip install opuslib
```

**Windows:**
```bash
# 使用预编译的wheel包
pip install opuslib
```

### 2. 测试Opus编码功能

运行测试脚本验证Opus编码是否正常：

```bash
python3 test_opus_encoding.py
```

预期输出：
```
✓ opuslib 导入成功
✓ Opus编码器初始化成功
✓ 生成测试PCM数据: 320 样本, 640 字节
✓ Opus编码成功:
  - 输入: 640 字节 PCM
  - 输出: 43 字节 Opus
  - 压缩比: 14.88:1
  - 编码时间: 0.12 ms
```

## 🚀 使用方法

### 1. 启动WebSocket音频服务器

**播放WAV文件：**
```bash
python3 websocket_audio_server.py your_audio.wav
```

**使用模拟音频数据：**
```bash
python3 websocket_audio_server.py
```

### 2. ESP32端配置

确保ESP32端的WebSocket配置正确：

```c
// 在main.c中配置服务器地址
SkWsSetServerIp("************", 8766);
```

### 3. 启动ESP32设备

编译并烧录ESP32固件，设备会自动连接到WebSocket服务器。

## 📊 数据格式

### WebSocket音频包格式

```c
typedef struct __attribute__((packed)) {
    uint8_t version;        // 0x01 - 协议版本
    uint8_t type;           // 0x01 - 音频类型
    uint16_t seqNum;        // 序列号（小端序）
    uint16_t payloadLen;    // Opus数据长度（小端序）
    uint16_t resv;          // 保留字段
    uint8_t data[0];        // 真正的Opus编码数据
} WsAudioPacket;
```

### Opus编码参数

- **采样率**: 16kHz
- **声道数**: 1（单声道）
- **帧长度**: 20ms（320样本）
- **应用类型**: VoIP
- **比特率**: 自适应

## 🔍 调试和故障排除

### 1. 检查Opus编码

如果没有音频输出，首先检查Opus编码是否正常：

```bash
python3 test_opus_encoding.py
```

### 2. 查看ESP32日志

ESP32端会输出详细的调试信息：

```
I (12345) WsAudio: Opus decode stats: success=100, error=0, seq=100, len=43
```

### 3. 常见问题

**问题1**: `ImportError: No module named 'opuslib'`
**解决**: 安装opuslib库：`pip install opuslib`

**问题2**: Opus编码失败
**解决**: 检查PCM数据格式，确保是320个int16样本

**问题3**: ESP32解码失败
**解决**: 检查Opus数据是否完整，查看ESP32日志中的错误信息

**问题4**: 音频断断续续
**解决**: 检查网络连接稳定性，调整发送间隔

## 📈 性能指标

### 典型性能数据

- **PCM帧大小**: 640字节（320样本 × 2字节）
- **Opus帧大小**: 20-80字节（取决于音频内容）
- **压缩比**: 8:1 到 20:1
- **编码延迟**: < 1ms
- **网络带宽**: ~2-8 kbps

### 音质评估

- **采样率**: 16kHz（电话音质）
- **比特率**: 动态调整（通常8-32 kbps）
- **延迟**: 总延迟 < 100ms（包括网络传输）

## 🔧 高级配置

### 调整Opus编码参数

可以在Python端调整Opus编码器参数：

```python
# 创建编码器时指定参数
encoder = opuslib.Encoder(
    fs=16000,                           # 采样率
    channels=1,                         # 声道数
    application=opuslib.APPLICATION_VOIP # 应用类型
)

# 设置比特率（可选）
encoder.bitrate = 16000  # 16 kbps
```

### ESP32端优化

在ESP32端可以调整音频缓冲区大小：

```c
// 在初始化时调整缓冲区参数
SkAudioInit(sizeof(uint16_t), 960);  // 增加缓冲区大小
```

## 📝 开发建议

1. **错误处理**: 始终检查Opus编码/解码的返回值
2. **缓冲管理**: 合理设置音频缓冲区大小，避免溢出
3. **网络优化**: 考虑网络抖动，实现自适应缓冲
4. **音质调优**: 根据应用场景调整Opus参数
5. **监控统计**: 记录编码/解码成功率，及时发现问题

## 🎉 验证成功

如果一切正常，您应该能看到：

1. Python端输出Opus编码成功的日志
2. ESP32端输出解码成功的统计信息
3. 听到清晰的音频播放

恭喜！您已经成功实现了基于真正Opus编码的WebSocket音频流！

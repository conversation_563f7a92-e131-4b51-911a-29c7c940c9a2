/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_rlink.h
 * @description: Relay链路接口.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#ifndef SK_RLINK_H
#define SK_RLINK_H
#include <stdint.h>

enum {
    RLINK_LINK_RUN = 0,
    RLINK_LINK_STOP = 1,
};

enum {
    RLINK_TASK_RUN = 0,
    RLINK_TASK_STOP = 1,
};

enum {
    RLINK_EVENT_STOP_CALL = 1,     // 停止通信
    RLINK_EVENT_TX_DATA = 2,       // 发送数据
    RLINK_EVENT_RX_EXIT = 3,       // 接收退出
};

typedef int32_t (*SkRlinkCodedDataCallback)(void *private, uint16_t sessionId,
    uint8_t *data, int32_t len, SkAudioDownlinkTimeRecord *timeRecord);

typedef void (*SkRlinkCodedDataEndCallback)(void *private, uint16_t sessionId);

typedef void* SkRlinkHandler;

void SkRlinkInit();
int SkRlinkEventNotify(uint8_t event, uint16_t param1);
void SkRlinkFeedReordAudio(void *handler, const uint8_t *data, size_t len, uint32_t timestamp);
void SkRlinkSetCodedDataCallback(SkRlinkCodedDataCallback cb, void *private);
void SkRlinkSetCodedDataEndCallback(SkRlinkCodedDataEndCallback cb, void *private);
SkRlinkHandler SkRlinkGetHandler();
void SkRlinkShowStat();
void SkRlinkSetFunFlag(uint8_t flag);
void SkRlinkSetPm(bool flag);

#endif //SK_RLINK_H
#!/usr/bin/env python3
"""
测试Opus编码功能
验证Python端的Opus编码是否正常工作
"""

import numpy as np
import struct
import time

try:
    import opuslib
    print("✓ opuslib 导入成功")
except ImportError:
    print("✗ opuslib 导入失败，请安装: pip install opuslib")
    exit(1)

def test_opus_encoding():
    """测试Opus编码功能"""
    print("=" * 50)
    print("Opus编码测试")
    print("=" * 50)
    
    try:
        # 初始化Opus编码器
        encoder = opuslib.Encoder(16000, 1, opuslib.APPLICATION_VOIP)
        print("✓ Opus编码器初始化成功")
        
        # 生成测试音频数据（1kHz正弦波）
        sample_rate = 16000
        frame_size = 320  # 20ms @ 16kHz
        frequency = 1000  # 1kHz
        
        # 生成一帧正弦波数据
        t = np.linspace(0, frame_size/sample_rate, frame_size, False)
        sine_wave = np.sin(2 * np.pi * frequency * t)
        
        # 转换为16位整数
        pcm_data = (sine_wave * 32767 * 0.5).astype(np.int16)
        pcm_bytes = pcm_data.tobytes()
        
        print(f"✓ 生成测试PCM数据: {len(pcm_data)} 样本, {len(pcm_bytes)} 字节")
        
        # Opus编码
        start_time = time.time()
        opus_data = encoder.encode(pcm_bytes, frame_size)
        encode_time = time.time() - start_time
        
        print(f"✓ Opus编码成功:")
        print(f"  - 输入: {len(pcm_bytes)} 字节 PCM")
        print(f"  - 输出: {len(opus_data)} 字节 Opus")
        print(f"  - 压缩比: {len(pcm_bytes)/len(opus_data):.2f}:1")
        print(f"  - 编码时间: {encode_time*1000:.2f} ms")
        
        # 显示Opus数据的前几个字节
        opus_hex = ' '.join([f'{b:02X}' for b in opus_data[:16]])
        print(f"  - Opus数据前16字节: {opus_hex}")
        
        # 创建WebSocket音频包
        version = 0x01
        audio_type = 0x01
        seq_num = 1
        payload_len = len(opus_data)
        reserved = 0x00
        
        # 打包头部（小端序）
        header = struct.pack('<BBHHH', 
                           version,        # version
                           audio_type,     # type
                           seq_num,        # seqNum
                           payload_len,    # payloadLen
                           reserved)       # resv
        
        packet = header + opus_data
        
        print(f"✓ WebSocket音频包创建成功:")
        print(f"  - 包头: {len(header)} 字节")
        print(f"  - 负载: {len(opus_data)} 字节")
        print(f"  - 总计: {len(packet)} 字节")
        
        # 显示包头内容
        header_hex = ' '.join([f'{b:02X}' for b in header])
        print(f"  - 包头内容: {header_hex}")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_multiple_frames():
    """测试多帧编码"""
    print("\n" + "=" * 50)
    print("多帧编码测试")
    print("=" * 50)
    
    try:
        encoder = opuslib.Encoder(16000, 1, opuslib.APPLICATION_VOIP)
        frame_count = 10
        total_opus_size = 0
        total_pcm_size = 0
        
        for i in range(frame_count):
            # 生成不同频率的正弦波
            frequency = 440 + i * 110  # 440Hz, 550Hz, 660Hz...
            t = np.linspace(0, 320/16000, 320, False)
            sine_wave = np.sin(2 * np.pi * frequency * t)
            pcm_data = (sine_wave * 32767 * 0.3).astype(np.int16)
            pcm_bytes = pcm_data.tobytes()
            
            opus_data = encoder.encode(pcm_bytes, 320)
            
            total_pcm_size += len(pcm_bytes)
            total_opus_size += len(opus_data)
            
            print(f"帧 {i+1:2d}: {frequency:4d}Hz -> PCM:{len(pcm_bytes):3d}字节 -> Opus:{len(opus_data):2d}字节")
        
        print(f"\n总计:")
        print(f"  - PCM总大小: {total_pcm_size} 字节")
        print(f"  - Opus总大小: {total_opus_size} 字节")
        print(f"  - 平均压缩比: {total_pcm_size/total_opus_size:.2f}:1")
        print(f"  - 平均Opus帧大小: {total_opus_size/frame_count:.1f} 字节")
        
        return True
        
    except Exception as e:
        print(f"✗ 多帧测试失败: {e}")
        return False

if __name__ == "__main__":
    print("Opus编码功能测试")
    print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 运行测试
    test1_ok = test_opus_encoding()
    test2_ok = test_multiple_frames()
    
    print("\n" + "=" * 50)
    print("测试结果")
    print("=" * 50)
    print(f"基础编码测试: {'✓ 通过' if test1_ok else '✗ 失败'}")
    print(f"多帧编码测试: {'✓ 通过' if test2_ok else '✗ 失败'}")
    
    if test1_ok and test2_ok:
        print("\n🎉 所有测试通过！Opus编码功能正常。")
        print("现在可以运行 websocket_audio_server.py 进行实际测试。")
    else:
        print("\n❌ 测试失败，请检查Opus库安装。")
